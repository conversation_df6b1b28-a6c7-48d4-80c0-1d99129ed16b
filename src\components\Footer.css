/* NAROOP Simplified Footer - Design System Implementation */
.naroop-footer-simplified {
  background: #FDFBF5; /* Light cream background */
  color: #591C28; /* Dark maroon text */
  margin-top: var(--space-2xl);
  padding: var(--space-2xl) 0 var(--space-lg) 0;
  border-top: 2px solid #6E8C65; /* Muted green accent border */
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-2xl);
  margin-bottom: var(--space-xl);
}

/* Brand Section */
.footer-brand {
  max-width: 400px;
}

.footer-brand-title {
  color: #591C28; /* Dark maroon */
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 var(--space-xs) 0;
  letter-spacing: 0.05em;
}

.footer-brand-subtitle {
  color: #6E8C65; /* Muted green */
  font-size: var(--text-sm);
  font-weight: 600;
  margin: 0 0 var(--space-md) 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.footer-mission {
  color: #591C28; /* Dark maroon */
  font-size: var(--text-base);
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

/* Links Section */
.footer-links-section {
  display: flex;
  gap: var(--space-xl);
}

.footer-links-group {
  flex: 1;
}

.footer-links-title {
  color: #591C28; /* Dark maroon */
  font-size: var(--text-lg);
  font-weight: 600;
  margin: 0 0 var(--space-md) 0;
  border-bottom: 2px solid #F7D046; /* Warm yellow accent */
  padding-bottom: var(--space-xs);
  display: inline-block;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-sm);
}

.footer-link {
  color: #591C28; /* Dark maroon */
  text-decoration: none;
  font-size: var(--text-base);
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: 50px; /* Pill-shaped design */
  border: 1px solid transparent;
}

.footer-link:hover {
  color: #FDFBF5; /* Light cream text */
  background: #6E8C65; /* Muted green background */
  border-color: #6E8C65;
  transform: translateY(-1px);
}

.footer-link:focus {
  outline: 2px solid #F7D046; /* Warm yellow focus */
  outline-offset: 2px;
}

/* Footer Bottom */
.footer-bottom {
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid #6E8C65; /* Muted green border */
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.footer-copyright {
  flex: 1;
}

.footer-copyright p {
  margin: 0;
  font-size: var(--text-sm);
  color: #591C28; /* Dark maroon */
  opacity: 0.8;
}

.footer-tagline {
  font-style: italic;
  color: #6E8C65; /* Muted green */
  margin-top: var(--space-xs) !important;
  font-weight: 500;
}

.footer-version {
  display: flex;
  align-items: center;
}

.version-badge {
  background: #F7D046; /* Warm yellow background */
  color: #591C28; /* Dark maroon text */
  padding: var(--space-xs) var(--space-sm);
  border-radius: 50px; /* Pill-shaped */
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  border: 1px solid #F7D046;
}

.dev-indicator {
  background: #591C28; /* Dark maroon */
  color: #FDFBF5; /* Light cream */
  padding: 2px 6px;
  border-radius: 50px; /* Pill-shaped */
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: var(--space-xs);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .footer-links-section {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 0 var(--space-md);
  }

  .footer-main {
    gap: var(--space-lg);
    text-align: center;
  }

  .footer-brand {
    max-width: none;
  }

  .footer-links-section {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }

  .footer-copyright {
    flex: none;
  }
}

@media (max-width: 480px) {
  .naroop-footer-simplified {
    padding: var(--space-xl) 0 var(--space-md) 0;
  }

  .footer-container {
    padding: 0 var(--space-sm);
  }

  .footer-brand-title {
    font-size: var(--text-xl);
  }

  .footer-links-title {
    font-size: var(--text-base);
  }

  .version-badge {
    font-size: 10px;
    padding: 4px 8px;
  }
}

/* Accessibility Features */
.footer-link:focus-visible {
  outline: 2px solid #F7D046; /* Warm yellow focus */
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .footer-link {
    transition: none;
  }

  .footer-link:hover {
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .naroop-footer-simplified {
    background: #FFFFFF;
    color: #000000;
    border-top: 3px solid #000000;
  }

  .footer-link {
    color: #000000;
    border-color: #000000;
  }

  .footer-link:hover {
    background: #000000;
    color: #FFFFFF;
  }

  .version-badge {
    background: #000000;
    color: #FFFFFF;
    border-color: #000000;
  }
}


