import React from 'react';
import { getFormattedVersion, getBuildEnvironment } from '../utils/version';
import './Footer.css';

const Footer = () => {
  const currentYear = 2025;
  const version = getFormattedVersion();
  const environment = getBuildEnvironment();

  return (
    <footer className="naroop-footer-simplified">
      <div className="footer-container">
        {/* Main Footer Content */}
        <div className="footer-main">
          {/* Brand Section */}
          <div className="footer-brand">
            <h3 className="footer-brand-title">NAROOP</h3>
            <p className="footer-brand-subtitle">Narrative of Our People</p>
            <p className="footer-mission">
              Empowering the African American community through authentic storytelling and connection.
            </p>
          </div>

          {/* Quick Links */}
          <div className="footer-links-section">
            <div className="footer-links-group">
              <h4 className="footer-links-title">Community</h4>
              <ul className="footer-links">
                <li><a href="#stories" className="footer-link">Stories</a></li>
                <li><a href="/kids" className="footer-link">Kids Zone</a></li>
                <li><a href="#economic" className="footer-link">Economic Hub</a></li>
              </ul>
            </div>

            <div className="footer-links-group">
              <h4 className="footer-links-title">Support</h4>
              <ul className="footer-links">
                <li><a href="#about" className="footer-link">About</a></li>
                <li><a href="#contact" className="footer-link">Contact</a></li>
                <li><a href="#privacy" className="footer-link">Privacy</a></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="footer-copyright">
              <p>&copy; {currentYear} NAROOP. All rights reserved.</p>
              <p className="footer-tagline">Celebrating Black excellence, one story at a time.</p>
            </div>

            <div className="footer-version">
              <span className="version-badge">
                {version}
                {environment === 'development' && (
                  <span className="dev-indicator">dev</span>
                )}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
